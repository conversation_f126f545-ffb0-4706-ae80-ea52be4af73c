#ifndef COMMON
#define COMMON

#include <pcl/point_types.h>

struct PointXYZLO {
    float x = 0.f;
    float y = 0.f;
    float z = 0.f;
    int label = 0;
    int object = 0;

    PointXYZLO(){}
    PointXYZLO(float x, float y, float z, int label, int object) :
        x(x), y(y), z(z), label(label), object(object) {}
EIGEN_MAKE_ALIGNED_OPERATOR_NEW
} EIGEN_ALIGN16;

struct _PointXYZRGBI {
    PCL_ADD_POINT4D;
    PCL_ADD_RGB;
    float intensity = 0;
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
} EIGEN_ALIGN16;

struct EIGEN_ALIGN16 PointXYZRGBI : public _PointXYZRGBI {
    inline PointXYZRGBI(const _PointXYZRGBI &p) {
        x = p.x; y = p.y; z = p.z; data[3] = 1.0f;
        rgba = p.rgba; intensity = p.intensity;
    }
    inline PointXYZRGBI(float x_, float y_, float z_, float intensity_ = 0.f,
                        std::uint8_t r_ = 255, std::uint8_t g_ = 255, std::uint8_t b_ = 255) {
        x = x_; y = y_; z = z_; data[3] = 1.0f;
        intensity = intensity_;
        r = r_; g = g_; b = b_; a = 255;
    }
    inline PointXYZRGBI(): PointXYZRGBI(0.f, 0.f, 0.f, 0.f, 255, 255, 255) {}
    PCL_MAKE_ALIGNED_OPERATOR_NEW
};

POINT_CLOUD_REGISTER_POINT_STRUCT(
    PointXYZRGBI,
    (float, x, x)
    (float, y, y)
    (float, z, z)
    (std::uint32_t, rgba, rgba)
    (float, intensity, intensity))

POINT_CLOUD_REGISTER_POINT_STRUCT(
    PointXYZLO,
    (float, x, x)
    (float, y, y)
    (float, z, z)
    (int, label, label)
    (int, object, object)
    )


#endif