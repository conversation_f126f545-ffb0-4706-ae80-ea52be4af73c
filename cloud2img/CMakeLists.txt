cmake_minimum_required(VERSION 3.0.0)
project(cloud2img)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED TRUE)

find_package(Boost REQUIRED COMPONENTS filesystem)
find_package(PCL 1.8 REQUIRED)
find_package(OpenCV REQUIRED)

include_directories(${CMAKE_SOURCE_DIR}/common/include)
include_directories(${PCL_INCLUDE_DIRS} ${Boost_INCLUDE_DIRS})
include_directories(${OpenCV_INCLUDE_DIRS})
link_directories(${PCL_LIBRARY_DIRS})
add_definitions(${PCL_DEFINITIONS})

#[[
add_executable(${PROJECT_NAME} cloud2img.cpp)
target_link_libraries(${PROJECT_NAME} ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})

add_executable(${PROJECT_NAME}_rs cloud2img_rs.cpp)
target_link_libraries(${PROJECT_NAME}_rs ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})

add_executable(${PROJECT_NAME}_i test/cloud2img_i.cpp)
target_link_libraries(${PROJECT_NAME}_i ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})

add_executable(max_entropy test/max_entropy.cpp)
target_link_libraries(max_entropy ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})

add_executable(semantic_cloud test/semantic_cloud.cpp)
target_link_libraries(semantic_cloud ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})

add_executable(semantic_cloud_v2 test/semantic_cloud_v2.cpp)
target_link_libraries(semantic_cloud_v2 ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})

add_executable(semantic_cloud_v3 semantic_cloud_v3.cpp)
target_link_libraries(semantic_cloud_v3 ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})

add_executable(concat concat.cpp)
target_link_libraries(concat ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})

add_executable(cloud_fusion cloud_fusion.cpp)
target_link_libraries(cloud_fusion ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})

add_executable(cloud2class cloud2class.cpp)
target_link_libraries(cloud2class ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})
]]

add_executable(semantic_cloud_xks semantic_cloud_xks.cpp)
target_link_libraries(semantic_cloud_xks ${PCL_LIBRARIES} ${OpenCV_LIBS} ${Boost_LIBRARIES})