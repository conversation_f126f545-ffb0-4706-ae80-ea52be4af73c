#!/bin/bash

# CSV_FILE="/home/<USER>/DinoV2/cloud2img/xks/camtraj_downsampled_-1_-1.csv"
CSV_FILE="./data/cam_trajk.csv"

n=1

while IFS=',' read -r x y z dir_x dir_y; do
  # 跳过标题行
  if [[ "$x" == "x" && "$y" == "y" ]]; then
    continue
  fi

  # output_folder="/home/<USER>/DinoV2/cloud2img/xks/road_-1_0_$n/"
  output_folder="./data/station_$n/"
  mkdir -p "$output_folder"
  echo "开始第 $n 个站点的模拟图片生成，虚拟相加参数：x=$x, y=$y, z=$z, dir_x=$dir_x, dir_y=$dir_y"
  
  ./build/cloud2img "/home/<USER>/Data/ytj/地形测试点云/小昆山北/preprocess/group_cloud_-1_-1.pcd" \
    "$output_folder" \
    "$x" "$y" "$z" "$dir_x" "$dir_y"
  
  echo "第 $n 条数据处理完成：x=$x, y=$y, z=$z, dir_x=$dir_x, dir_y=$dir_y"

  ((n++))
done < "$CSV_FILE"
