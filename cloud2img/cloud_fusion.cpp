#include <unordered_map>
#include <tuple>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/io/pcd_io.h>
#include <opencv2/opencv.hpp>


struct PointXYZRGBHash {
    std::size_t operator()(const std::tuple<float, float, float>& point) const {
        std::size_t h1 = std::hash<float>{}(std::get<0>(point));
        std::size_t h2 = std::hash<float>{}(std::get<1>(point));
        std::size_t h3 = std::hash<float>{}(std::get<2>(point));
        return h1 ^ (h2 << 1) ^ (h3 << 2);
    }
};

struct Vec3bHash {
    std::size_t operator()(const cv::Vec3b& color) const {
        std::size_t h1 = std::hash<uchar>{}(color[0]);
        std::size_t h2 = std::hash<uchar>{}(color[1]);
        std::size_t h3 = std::hash<uchar>{}(color[2]);
        return h1 ^ (h2 << 1) ^ (h3 << 2);
    }
};

void cloudFusion(pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud,
                                 pcl::PointCloud<pcl::PointXYZRGB>::Ptr& label_votes_cloud,
                                 pcl::PointCloud<pcl::PointXYZRGB>::Ptr& max_votes_cloud,
                                 pcl::PointCloud<pcl::PointXYZRGB>::Ptr& fused_cloud) {
    std::unordered_map<std::tuple<float, float, float>, std::unordered_map<cv::Vec3b, int, Vec3bHash>, PointXYZRGBHash> point_label_votes;

    for (const auto& point : cloud->points) {
        auto point_key = std::make_tuple(point.x, point.y, point.z);
        cv::Vec3b color(point.r, point.g, point.b);
        point_label_votes[point_key][color]++;
    }

    for (const auto& kv : point_label_votes) {
        const auto& point_key = kv.first;
        const auto& label_votes = kv.second;

        pcl::PointXYZRGB point;
        std::tie(point.x, point.y, point.z) = point_key;

        if (label_votes.size() > 1) {
            pcl::PointXYZRGB label_vote_point = point;
            label_vote_point.r = 255;  
            label_vote_point.g = 0;
            label_vote_point.b = 0;
            label_votes_cloud->points.push_back(label_vote_point);
        }

        float max_votes = 0;
        cv::Vec3b best_color;
        for (const auto& lv : label_votes) {
            if (lv.second > max_votes) {
                max_votes = lv.second;
                best_color = lv.first;
            }
        }

        pcl::PointXYZRGB fused_point = point;
        fused_point.r = best_color[0];
        fused_point.g = best_color[1];
        fused_point.b = best_color[2];
        fused_cloud->points.push_back(fused_point);

        if (max_votes > 1) {
            pcl::PointXYZRGB max_vote_point = point;
            max_vote_point.r = 0;  
            max_vote_point.g = 0;
            max_vote_point.b = 255;
            max_votes_cloud->points.push_back(max_vote_point);
        }
    }

    label_votes_cloud->width = label_votes_cloud->points.size();
    label_votes_cloud->height = 1;
    label_votes_cloud->is_dense = true;

    max_votes_cloud->width = max_votes_cloud->points.size();
    max_votes_cloud->height = 1;
    max_votes_cloud->is_dense = true;

    fused_cloud->width = fused_cloud->points.size();
    fused_cloud->height = 1;
    fused_cloud->is_dense = true;
}

int main(int argc, char** argv) {
    std::string inputPath = argv[1];
    std::string outputPath = argv[2];

    pcl::PointCloud<pcl::PointXYZRGB>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr label_votes_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr max_votes_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr fused_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);

    pcl::io::loadPCDFile(inputPath, *cloud); 
    cloudFusion(cloud, label_votes_cloud, max_votes_cloud, fused_cloud);

    std::string multi_class_cloud_path = outputPath + "multi_class_cloud.pcd";
    pcl::io::savePCDFile(multi_class_cloud_path, *label_votes_cloud);
    std::string multi_votes_cloud_path = outputPath + "multi_votes_cloud.pcd";
    pcl::io::savePCDFile(multi_votes_cloud_path, *max_votes_cloud);
    std::string fusion_cloud_path = outputPath + "fusion_cloud.pcd";
    pcl::io::savePCDFile(fusion_cloud_path, *fused_cloud);

    return 0;
}
