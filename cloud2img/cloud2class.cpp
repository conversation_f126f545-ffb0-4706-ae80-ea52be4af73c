#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <pcl/io/pcd_io.h>
#include <pcl/point_types.h>
#include <opencv2/opencv.hpp> 
#include <boost/filesystem.hpp>

std::vector<cv::Vec3b> define_color_groups() {
    return {
        {cv::Vec3b(120, 120, 120)}, {cv::Vec3b(180, 120, 120)}, {cv::Vec3b(6, 230, 230)},
        {cv::Vec3b(4, 200, 3)}, {cv::Vec3b(140, 140, 140)}, {cv::Vec3b(230, 230, 230)},
        {cv::Vec3b(4, 250, 7)}, {cv::Vec3b(235, 255, 7)}, {cv::Vec3b(150, 5, 61)},
        {cv::Vec3b(120, 120, 70)}, {cv::Vec3b(8, 255, 51)}, {cv::Vec3b(0, 102, 200)},
        {cv::Vec3b(61, 230, 250)}, {cv::Vec3b(255, 184, 6)}, {cv::Vec3b(255, 5, 153)},
        {cv::Vec3b(255, 82, 0)}, {cv::Vec3b(194, 255, 0)}, {cv::Vec3b(51, 0, 255)},
    };
}

bool is_color_in_group(const pcl::PointXYZRGB& point, const cv::Vec3b& color) {
    return (point.r == color[0] && point.g == color[1] && point.b == color[2]);
}

void save_grouped_pcds(
    const std::vector<pcl::PointCloud<pcl::PointXYZRGB>::Ptr>& grouped_clouds,
    const std::string& output_directory,
    const std::string& base_filename) {
    for (size_t i = 0; i < grouped_clouds.size(); ++i) {
        const auto& cloud = grouped_clouds[i];
        if (cloud->points.empty()) {
            continue;
        }
        std::string filename = output_directory + "/" + base_filename + "_group_" + std::to_string(i + 1) + ".pcd";
        pcl::io::savePCDFileASCII(filename, *cloud);
        std::cout << "Saved " << cloud->points.size() << " points to " << filename << std::endl;
    }
}

int main(int argc, char** argv) {
    if (argc != 2) {
        std::cerr << "Usage: " << argv[0] << " input.pcd" << std::endl;
        return -1;
    }

    std::string input_pcd = argv[1];

    boost::filesystem::path input_path(input_pcd);
    if (!boost::filesystem::exists(input_path) || input_path.extension() != ".pcd") {
        std::cerr << "Input file does not exist or is not a .pcd file." << std::endl;
        return -1;
    }

    std::string output_directory = input_path.parent_path().string();
    std::string base_filename = input_path.stem().string();

    pcl::PointCloud<pcl::PointXYZRGB>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    if (pcl::io::loadPCDFile<pcl::PointXYZRGB>(input_pcd, *cloud) == -1) {
        PCL_ERROR("Couldn't read input file\n");
        return -1;
    }

    auto color_groups = define_color_groups();
    std::vector<pcl::PointCloud<pcl::PointXYZRGB>::Ptr> grouped_clouds(color_groups.size());
    for (auto& group : grouped_clouds) {
        group = pcl::PointCloud<pcl::PointXYZRGB>::Ptr(new pcl::PointCloud<pcl::PointXYZRGB>);
    }

    for (const auto& point : cloud->points) {
        for (size_t i = 0; i < color_groups.size(); ++i) {
            if (is_color_in_group(point, color_groups[i])) {
                grouped_clouds[i]->points.push_back(point);
                break;
            }
        }
    }

    for (auto& group : grouped_clouds) {
        group->width = group->points.size();
        group->height = 1;
        group->is_dense = false;
    }

    save_grouped_pcds(grouped_clouds, output_directory, base_filename);
    return 0;
}
