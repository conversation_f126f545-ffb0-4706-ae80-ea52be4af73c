import open3d as o3d
import numpy as np

def main():
    # Load the point cloud data
    cloud = o3d.io.read_point_cloud("../dinov2/scene2/road2_1/whole_road.pcd")  

    points = np.asarray(cloud.points)

    cov_matrix = np.cov(points.T) 

    eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

    main_direction = eigenvectors[:, np.argmax(eigenvalues)]

    print("Main Direction:", main_direction)

    mean_point = np.mean(points, axis=0)
    arrow = o3d.geometry.TriangleMesh.create_arrow(cylinder_radius=0.1, cone_radius=0.2, cylinder_height=2, cone_height=0.8)
    arrow.paint_uniform_color([1, 0, 0])  # Red color for the main direction
    arrow.translate(mean_point - 5 * main_direction)  # Positioning the arrow
    arrow.scale(10, center=mean_point)  # Scaling to make the arrow longer

    geometries = [cloud, arrow]
    o3d.visualization.draw_geometries(geometries, window_name="Point Cloud with Main Direction Visualization")


if __name__ == "__main__":
    main()
