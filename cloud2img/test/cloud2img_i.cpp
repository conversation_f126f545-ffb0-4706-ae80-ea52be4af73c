#include <pcl/io/pcd_io.h>
#include <pcl/point_types.h>
#include <pcl/common/transforms.h>
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <boost/filesystem.hpp>
#include <vector>
#include <iostream>
#include <limits>
#include <string>

#include "common.h"

std::vector<Eigen::Affine3f> pcTransform(int num_rot, const Eigen::Vector3f& translation) {
    std::vector<Eigen::Affine3f> transformations;
    float deltaAngle = 2 * M_PI / num_rot;

    for (int i = 0; i < num_rot; i++) {
        float angle = i * deltaAngle;
        Eigen::Affine3f trans = Eigen::Affine3f::Identity();
        trans.translate(-translation);

        Eigen::Affine3f rotation = Eigen::Affine3f::Identity();
        rotation.rotate(Eigen::AngleAxisf(angle, Eigen::Vector3f(0, 0, 1)));

        Eigen::Affine3f combined = rotation * trans;
        transformations.push_back(combined);
    }

    return transformations;
}

void pc2img(pcl::PointCloud<PointXYZRGBI>::Ptr cloud, const Eigen::Affine3f& transform, 
                            const std::string& file_name) {
    pcl::PointCloud<PointXYZRGBI>::Ptr transformed_cloud(new pcl::PointCloud<PointXYZRGBI>);
    pcl::transformPointCloud(*cloud, *transformed_cloud, transform);

    // int width = 1920, height = 1080;
    int width = 1024, height = 1024;

    cv::Mat image(height, width, CV_8UC1, cv::Scalar(0));
    cv::Mat depthBuffer(height, width, CV_32F, cv::Scalar(std::numeric_limits<float>::max()));

    
    float focalLength = width / (2.0 * tan(90.0 * M_PI / 360.0));
    float centerX = width / 2.0;
    float centerY = height / 2.0;

    for (const auto& point : transformed_cloud->points) {
        float x = width - (focalLength * point.y / point.x + centerX);
        float y = height - (focalLength * point.z / point.x + centerY);;

        if (x >= 0 && x < width && y >= 0 && y < height && point.x > 0) {
            int xi = static_cast<int>(x);
            int yi = static_cast<int>(y);
            float& pixelDepth = depthBuffer.at<float>(yi, xi);
            if (point.x < pixelDepth) { 
                pixelDepth = point.x;
                uint8_t& pixelIntensity = image.at<uint8_t>(yi, xi);
                // pixelIntensity = static_cast<uint8_t>(255 - point.intensity);
                pixelIntensity = static_cast<uint8_t>(point.intensity);
            }
        }
    }
  
    cv::imwrite(file_name, image);
}

int main(int argc, char** argv) {

    pcl::PointCloud<PointXYZRGBI>::Ptr cloud(new pcl::PointCloud<PointXYZRGBI>);
    if (pcl::io::loadPCDFile<PointXYZRGBI>(argv[1], *cloud) == -1) {
        PCL_ERROR("Couldn't read file\n");
        return -1;
    }

    float cx = std::stof(argv[3]);
    float cy = std::stof(argv[4]);
    float cz = std::stof(argv[5]);
    Eigen::Vector3f translation(cx, cy, cz);

    std::string basePath = argv[2];
    boost::filesystem::path dir(basePath);
    if (!boost::filesystem::exists(dir)) {
        if (!boost::filesystem::create_directories(dir)) {
            std::cerr << "Failed to create directories: " << basePath << std::endl;
        }
    }

    int num_rot = 4;
    auto rotations = pcTransform(num_rot, translation);

    for (int i = 0; i < rotations.size(); ++i) {
        std::string file_name = basePath + std::to_string(i) + ".png";
        pc2img(cloud, rotations[i], file_name);
    }

    return 0;
}