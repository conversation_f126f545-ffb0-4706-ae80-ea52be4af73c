#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/io/pcd_io.h>
#include <pcl/filters/voxel_grid.h>
#include <opencv2/opencv.hpp>
#include <Eigen/Dense>
#include <boost/filesystem.hpp>

std::vector<Eigen::Vector2i> readCSV(const std::string& filename, std::vector<Eigen::Vector3f>& points) {
    std::vector<Eigen::Vector2i> pixel_coords;
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filename << std::endl;
        return pixel_coords;
    }

    std::string line;
    bool first_line = true;
    while (std::getline(file, line)) {
        if (first_line) {
            first_line = false;
            continue;
        }

        std::stringstream ss(line);
        std::string token;
        Eigen::Vector3f point;
        Eigen::Vector2i pixel_coord;

        std::getline(ss, token, ',');
        pixel_coord.x() = std::stoi(token);
        std::getline(ss, token, ',');
        pixel_coord.y() = std::stoi(token);

        std::getline(ss, token, ',');
        point.x() = std::stof(token);
        std::getline(ss, token, ',');
        point.y() = std::stof(token);
        std::getline(ss, token, ',');
        point.z() = std::stof(token);
        points.push_back(point);
        pixel_coords.push_back(pixel_coord);
    }
    file.close();
    // std::cout << pixel_coords.size() << " " << points.size() << std::endl;
    return pixel_coords;
}

std::string findCSV(const std::string& image_name, const std::string& csv_folder) {
    std::string base_name = boost::filesystem::basename(image_name);
    std::string directory_part = base_name.substr(0, base_name.find_last_of('_'));
    std::string file_part = base_name.substr(base_name.find_last_of('_'));
    std::string csv_path = csv_folder + directory_part + "/" + file_part + ".png.csv";
    return csv_path;
}

void main_direction(pcl::PointCloud<pcl::PointXYZRGB>::Ptr whole_road_cloud, std::string input_image_folder){
    if (!whole_road_cloud->points.empty()) {
        std::vector<Eigen::Vector3f> road_points;
        for (const auto& point : whole_road_cloud->points) {
            road_points.push_back(Eigen::Vector3f(point.x, point.y, point.z));
        }

        Eigen::MatrixXf mat(road_points.size(), 2);
        Eigen::Vector2f mean = Eigen::Vector2f::Zero();

        for (size_t i = 0; i < road_points.size(); ++i) {
            mat(i, 0) = road_points[i].x();
            mat(i, 1) = road_points[i].y();
            mean += mat.row(i);
        }
        mean /= road_points.size();

        for (size_t i = 0; i < road_points.size(); ++i) {
            mat.row(i) -= mean.transpose();
        }

        Eigen::Matrix2f cov_matrix = mat.transpose() * mat;
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix2f> solver(cov_matrix);
        Eigen::Vector2f main_direction = solver.eigenvectors().col(1);

        std::cout << "Main direction for whole road cloud: " << main_direction.transpose() << std::endl;

        Eigen::Matrix2f rotation_matrix;
        rotation_matrix << main_direction(0), -main_direction(1),
                        main_direction(1),  main_direction(0);

        std::vector<Eigen::Vector3f> divided_points;
        for (auto& point : road_points) {
            Eigen::Vector2f xy = rotation_matrix * Eigen::Vector2f(point.x(), point.y());
            point.x() = xy.x();
            point.y() = xy.y();
        }

        Eigen::Vector3f start_point = Eigen::Vector3f(mean.x(), mean.y(), 0);
        Eigen::Vector2f rotated_point = rotation_matrix * Eigen::Vector2f(start_point.x(), start_point.y());

        float start_projection = rotated_point.x();
        float furthest_projection = start_projection;
        float max_distance = 0;

        for (const auto& point : road_points) {
            float projection = point.x(); 
            float distance = std::abs(projection - start_projection);
            if (distance > max_distance) {
                max_distance = distance;
                furthest_projection = projection;
            }
        }

        int num_segments = 5;
        Eigen::Vector2f direction = (furthest_projection > start_projection) ? Eigen::Vector2f(1, 0) : Eigen::Vector2f(-1, 0);

        for (int i = 0; i <= num_segments; ++i) {
            Eigen::Vector2f rotated_new_point = rotated_point + direction * (i * max_distance / num_segments);
            Eigen::Vector2f original_new_point = rotation_matrix.inverse() * rotated_new_point;
            divided_points.push_back(Eigen::Vector3f(original_new_point.x(), original_new_point.y(), start_point.z()));
        }

        pcl::PointCloud<pcl::PointXYZRGB>::Ptr direction_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
        for (const auto& point : divided_points) {
            pcl::PointXYZRGB line_point;
            line_point.x = point.x();
            line_point.y = point.y();
            line_point.z = point.z();
            line_point.r = 0; 
            line_point.g = 0;
            line_point.b = 255; 
            direction_cloud->points.push_back(line_point);
        }
        direction_cloud->width = direction_cloud->points.size();
        direction_cloud->height = 1;
        std::string direction_pcd = input_image_folder + "whole_road_direction.pcd";
        pcl::io::savePCDFile(direction_pcd, *direction_cloud);

        whole_road_cloud->width = whole_road_cloud->points.size();
        whole_road_cloud->height = 1;
        std::string whole_road_pcd = input_image_folder + "whole_road.pcd";
        pcl::io::savePCDFile(whole_road_pcd, *whole_road_cloud);
        std::cout << "Saved whole road point cloud with " << whole_road_cloud->points.size() << " data points to " << whole_road_pcd << std::endl;
    }
}

int main(int argc, char** argv) {
    if (argc < 3) {
        std::cerr << "Usage: " << argv[0] << " <input_csv_folder> <input_image_folder> " << std::endl;
        return -1;
    }

    std::string input_csv_folder = argv[1];
    std::string input_image_folder = argv[2];

    pcl::PointCloud<pcl::PointXYZRGB>::Ptr whole_road_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);

    for (auto& entry : boost::filesystem::directory_iterator(input_image_folder)) {
        std::string image_name = entry.path().filename().string();
        if (boost::filesystem::is_regular_file(entry) && entry.path().extension() == ".png" && image_name.find("overlap") == std::string::npos) {
            std::string input_image = entry.path().string();
            

            std::string input_csv = findCSV(image_name, input_csv_folder);

            if (!boost::filesystem::exists(input_csv)) {
                std::cerr << "CSV file not found for image: " << image_name << std::endl;
                continue;
            }

            std::vector<Eigen::Vector3f> points;
            std::vector<Eigen::Vector2i> pixel_coords = readCSV(input_csv, points);

            cv::Mat image = cv::imread(input_image, cv::IMREAD_COLOR);
            if (image.empty()) {
                std::cerr << "Failed to open image: " << input_image << std::endl;
                continue;
            }

            pcl::PointCloud<pcl::PointXYZRGB>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
            std::vector<Eigen::Vector3f> road_points;

            for (size_t i = 0; i < points.size(); ++i) {
                pcl::PointXYZRGB point;
                point.x = points[i].x();
                point.y = points[i].y();
                point.z = points[i].z();

                cv::Vec3b color = image.at<cv::Vec3b>(pixel_coords[i].y(), pixel_coords[i].x());
                point.r = color[2];
                point.g = color[1];
                point.b = color[0];

                if (color == cv::Vec3b(140, 140, 140)) {
                    road_points.push_back(points[i]);
                    // pcl::PointXYZRGB road_point;
                    // road_point.x = points[i].x();
                    // road_point.y = points[i].y();
                    // road_point.z = points[i].z();
                    // whole_road_cloud->points.push_back(road_point);
                }

                cloud->points.push_back(point);
            }

            std::cout << "road_points: " << road_points.size() << std::endl;
            std::vector<Eigen::Vector3f> divided_points;
            if (road_points.size() > 10000) {

                // pcl::VoxelGrid<pcl::PointXYZRGB> vox;
                // vox.setInputCloud(cloud);
                // vox.setLeafSize(0.5f, 0.5f, 0.5f);
                // vox.filter(*cloud); 

                Eigen::MatrixXf mat(road_points.size(), 2);
                Eigen::Vector2f mean = Eigen::Vector2f::Zero();

                for (size_t i = 0; i < road_points.size(); ++i) {
                    mat(i, 0) = road_points[i].x();
                    mat(i, 1) = road_points[i].y();
                    mean += mat.row(i);
                }
                mean /= road_points.size();

                for (size_t i = 0; i < road_points.size(); ++i) {
                    mat.row(i) -= mean.transpose();
                }

                Eigen::Matrix2f cov_matrix = mat.transpose() * mat;
                Eigen::SelfAdjointEigenSolver<Eigen::Matrix2f> solver(cov_matrix);
                Eigen::Vector2f main_direction = solver.eigenvectors().col(1);
                float angle = -std::atan2(main_direction.y(), main_direction.x());
                
                Eigen::Matrix2f rotation_matrix;
                rotation_matrix << std::cos(angle), -std::sin(angle),
                                   std::sin(angle),  std::cos(angle);

                std::cout << "Main direction for " << image_name << ": " << main_direction.transpose() << std::endl;

                // Eigen::Matrix2f rotation_matrix;
                // rotation_matrix << main_direction(0), -main_direction(1),
                //                 main_direction(1),  main_direction(0);

                for (auto& point : road_points) {
                    Eigen::Vector2f xy = rotation_matrix * Eigen::Vector2f(point.x(), point.y());
                    point.x() = xy.x();
                    point.y() = xy.y();
                }

                // Eigen::Vector3f start_point = Eigen::Vector3f(std::stof(argv[3]), std::stof(argv[4]), std::stof(argv[5]));
                Eigen::Vector3f start_point = Eigen::Vector3f(mean.x(), mean.y(), std::stof(argv[3]));
                Eigen::Vector2f rotated_point = rotation_matrix * Eigen::Vector2f(start_point.x(), start_point.y());

                float start_projection = rotated_point.x();
                float furthest_projection = start_projection;
                float max_distance = 0;

                for (const auto& point : road_points) {
                    float projection = point.x(); 
                    float distance = std::abs(projection - start_projection);
                    if (distance > max_distance) {
                        max_distance = distance;
                        furthest_projection = projection;
                    }
                }

                int num_segments = static_cast<int>(max_distance / 20);
                Eigen::Vector2f direction = (furthest_projection > start_projection) ? Eigen::Vector2f(1, 0) : Eigen::Vector2f(-1, 0);

                for (int i = 0; i <= num_segments; ++i) {
                    Eigen::Vector2f rotated_new_point = rotated_point + direction * (i * 20);
                    Eigen::Vector2f original_new_point = rotation_matrix.inverse() * rotated_new_point;
                    divided_points.push_back(Eigen::Vector3f(original_new_point.x(), original_new_point.y(), start_point.z()));
                }

                std::cout << "Divided points:" << std::endl;
                for (const auto& point : divided_points) {
                    std::cout << point.transpose() << std::endl;
                }

                if (divided_points.size() > 1) {
                    pcl::PointCloud<pcl::PointXYZRGB>::Ptr direction_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
                    for (const auto& point : divided_points) {
                        pcl::PointXYZRGB line_point;
                        line_point.x = point.x();
                        line_point.y = point.y();
                        line_point.z = point.z();
                        line_point.r = 255; 
                        line_point.g = 0;
                        line_point.b = 0;
                        direction_cloud->points.push_back(line_point);
                    }
                    direction_cloud->width = direction_cloud->points.size();
                    direction_cloud->height = 1;
                    std::string direction_pcd = input_image_folder + boost::filesystem::basename(image_name) + "_direction.pcd";
                    pcl::io::savePCDFile(direction_pcd, *direction_cloud);
                }
            }

            cloud->width = cloud->points.size();
            cloud->height = 1;
            std::string output_pcd = input_image_folder + boost::filesystem::basename(image_name) + ".pcd";
            pcl::io::savePCDFile(output_pcd, *cloud);
            std::cout << "Saved " << cloud->points.size() << " data points to " << output_pcd << std::endl;
        }
    }
    
    // main_direction(whole_road_cloud, input_image_folder);

    return 0;
}