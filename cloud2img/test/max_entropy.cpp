#include <iostream>
#include <vector>
#include <cmath>
#include <limits>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/io/pcd_io.h>
#include "common.h"

void calHist(const pcl::PointCloud<PointXYZRGBI>::Ptr& cloud, std::vector<int>& histogram, int numBins) {
    for (const auto& point : cloud->points) {
        int intensity = static_cast<int>(point.intensity);
        if (intensity >= 0 && intensity < numBins) {
            histogram[intensity]++;
        }
    }
}

double calCurEntropy(const std::vector<int>& histogram, int thresh) {
    double backSum = 0, targetSum = 0;
    for (int i = 0; i < thresh; i++) {
        backSum += histogram[i];
    }
    for (int i = thresh; i < histogram.size(); i++) {
        targetSum += histogram[i];
    }

    double backEntropy = 0, targetEntropy = 0;
    for (int i = 0; i < thresh; i++) {
        if (histogram[i] > 0) {
            double probability = histogram[i] / backSum;
            backEntropy -= probability * log2(probability);
        }
    }
    for (int i = thresh; i < histogram.size(); i++) {
        if (histogram[i] > 0) {
            double probability = histogram[i] / targetSum;
            targetEntropy -= probability * log2(probability);
        }
    }
    return (targetEntropy + backEntropy);
}

int maxEntroThresh(const pcl::PointCloud<PointXYZRGBI>::Ptr& cloud) {
    const int numBins = 256;
    std::vector<int> histogram(numBins, 0);
    calHist(cloud, histogram, numBins);

    double maxEntropy = -std::numeric_limits<double>::infinity();
    int bestThresh = 0;
    for (int i = 1; i < numBins; i++) {
        double entropy = calCurEntropy(histogram, i);
        if (entropy > maxEntropy) {
            maxEntropy = entropy;
            bestThresh = i;
        }
    }
    return bestThresh;
}

pcl::PointCloud<PointXYZRGBI>::Ptr extractPoints(const pcl::PointCloud<PointXYZRGBI>::Ptr& cloud, int thresh) {
    pcl::PointCloud<PointXYZRGBI>::Ptr result(new pcl::PointCloud<PointXYZRGBI>);
    for (const auto& point : cloud->points) {
        if (point.intensity >= thresh) {
            result->points.push_back(point);
        }
    }
    return result;
}

int main(int argc, char** argv) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <input_pcd_file>" << std::endl;
        return -1;
    }

    pcl::PointCloud<PointXYZRGBI>::Ptr cloud(new pcl::PointCloud<PointXYZRGBI>);
    if (pcl::io::loadPCDFile<PointXYZRGBI>(argv[1], *cloud) == -1) {
        PCL_ERROR("Couldn't read file %s \n", argv[1]);
        return -1;
    }

    int thresh = maxEntroThresh(cloud);
    std::cout << "Max Entropy thresh: " << thresh << std::endl;

    pcl::PointCloud<PointXYZRGBI>::Ptr target_Points = extractPoints(cloud, thresh);

    pcl::io::savePCDFileASCII("high_intensity_points.pcd", *target_Points);
    std::cout << "Saved " << target_Points->points.size() << " high intensity points to high_intensity_points.pcd" << std::endl;

    return 0;
}
