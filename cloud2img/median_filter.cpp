#include <pcl/kdtree/kdtree_flann.h>

void applyMedianFilter(pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud, float radius) {
    pcl::KdTreeFLANN<pcl::PointXYZRGB> kdtree;
    kdtree.setInputCloud(cloud);

    pcl::PointCloud<pcl::PointXYZRGB>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);

    for (const auto& point : cloud->points) {
        std::vector<int> neighbor_indices;
        std::vector<float> neighbor_distances;
        kdtree.radiusSearch(point, radius, neighbor_indices, neighbor_distances);

        std::unordered_map<cv::Vec3b, int, cv::Vec3bHash> label_counts;
        for (int idx : neighbor_indices) {
            cv::Vec3b color(cloud->points[idx].r, cloud->points[idx].g, cloud->points[idx].b);
            label_counts[color]++;
        }

        int max_count = 0;
        cv::Vec3b best_color(point.r, point.g, point.b);
        for (const auto& lc : label_counts) {
            if (lc.second > max_count) {
                max_count = lc.second;
                best_color = lc.first;
            }
        }

        pcl::PointXYZRGB filtered_point = point;
        filtered_point.r = best_color[0];
        filtered_point.g = best_color[1];
        filtered_point.b = best_color[2];
        filtered_cloud->points.push_back(filtered_point);
    }

    filtered_cloud->width = filtered_cloud->points.size();
    filtered_cloud->height = 1;
    filtered_cloud->is_dense = true;
    cloud = filtered_cloud;
}
