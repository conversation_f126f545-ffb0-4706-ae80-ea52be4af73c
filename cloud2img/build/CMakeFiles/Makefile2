# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/DinoV2/cloud2img

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/DinoV2/cloud2img/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/semantic_cloud_xks.dir/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall:

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/semantic_cloud_xks.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/semantic_cloud_xks.dir

# All Build rule for target.
CMakeFiles/semantic_cloud_xks.dir/all:
	$(MAKE) -f CMakeFiles/semantic_cloud_xks.dir/build.make CMakeFiles/semantic_cloud_xks.dir/depend
	$(MAKE) -f CMakeFiles/semantic_cloud_xks.dir/build.make CMakeFiles/semantic_cloud_xks.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/DinoV2/cloud2img/build/CMakeFiles --progress-num=1,2 "Built target semantic_cloud_xks"
.PHONY : CMakeFiles/semantic_cloud_xks.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/semantic_cloud_xks.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/DinoV2/cloud2img/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/semantic_cloud_xks.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/DinoV2/cloud2img/build/CMakeFiles 0
.PHONY : CMakeFiles/semantic_cloud_xks.dir/rule

# Convenience name for target.
semantic_cloud_xks: CMakeFiles/semantic_cloud_xks.dir/rule

.PHONY : semantic_cloud_xks

# clean rule for target.
CMakeFiles/semantic_cloud_xks.dir/clean:
	$(MAKE) -f CMakeFiles/semantic_cloud_xks.dir/build.make CMakeFiles/semantic_cloud_xks.dir/clean
.PHONY : CMakeFiles/semantic_cloud_xks.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

