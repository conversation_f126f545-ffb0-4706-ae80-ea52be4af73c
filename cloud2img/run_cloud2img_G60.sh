#!/bin/bash

CSV_FILE="/home/<USER>/DinoV2/cloud2img/G60/camtraj_downsampled_-1_0.csv"

n=1

while IFS=',' read -r x y z dir_x dir_y; do
  # 跳过标题行
  if [[ "$x" == "x" && "$y" == "y" ]]; then
    continue
  fi

  output_folder="/home/<USER>/DinoV2/cloud2img/G60/road_-1_0_$n/"
  mkdir -p "$output_folder"

  ./build/cloud2img "/home/<USER>/bev_seg/地形测试点云/G60地形/preprocess/group_cloud_-1_0.pcd" \
    "$output_folder" \
    "$x" "$y" "$z" "$dir_x" "$dir_y"
  
  echo "第 $n 条数据处理完成：x=$x, y=$y, z=$z, dir_x=$dir_x, dir_y=$dir_y"

  ((n++))
done < "$CSV_FILE"
