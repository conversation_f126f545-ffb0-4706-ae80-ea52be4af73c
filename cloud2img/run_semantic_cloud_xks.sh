#!/bin/bash

# 设置输入文件夹和输出文件夹的路径
input_folder="/home/<USER>/DinoV2/cloud2img/xks/"
output_folder="/home/<USER>/DinoV2/dinov2-main/output_xks/"

# 遍历 output_folder 文件夹下所有以 road 开头的文件夹
for dir in ${output_folder}road*/; do
    # 确保是文件夹并且是以 "road" 开头
    if [ -d "$dir" ]; then
        # 获取文件夹名称（去掉路径中的前缀部分）
        folder_name=$(basename "$dir")

        # 运行 ./semantic_cloud_xks 命令
        echo "Running for folder: $folder_name"
        ./build/semantic_cloud_xks "${input_folder}${folder_name}/" "$dir" 0.55
    fi
done
