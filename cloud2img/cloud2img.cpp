#include <pcl/io/pcd_io.h>
#include <pcl/point_types.h>
#include <pcl/common/transforms.h>
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <boost/filesystem.hpp>
#include <vector>
#include <iostream>
#include <limits>
#include <string>
#include <unordered_map>

#include "common.h"

std::vector<Eigen::Affine3f> pcTransform(int num_rot, const Eigen::Vector3f& translation, float prior_angle) {
    std::vector<Eigen::Affine3f> transformations;
    float deltaAngle = 2 * M_PI / num_rot;

    for (int i = 0; i < num_rot; i++) {
        float angle = i * deltaAngle + prior_angle;
        Eigen::Affine3f trans = Eigen::Affine3f::Identity();
        trans.translate(-translation);

        Eigen::Affine3f rotation = Eigen::Affine3f::Identity();
        rotation.rotate(Eigen::AngleAxisf(angle, Eigen::Vector3f(0, 0, 1)));
        Eigen::Affine3f combined = rotation * trans;
        transformations.push_back(combined);
    }

    return transformations;
}

void pc2img(pcl::PointCloud<PointXYZRGBI>::Ptr cloud, const Eigen::Affine3f& transform, 
                            const std::string& file_name) {
    pcl::PointCloud<PointXYZRGBI>::Ptr transformed_cloud(new pcl::PointCloud<PointXYZRGBI>);
    pcl::transformPointCloud(*cloud, *transformed_cloud, transform);

    // int width = 1920, height = 1080;
    int width = 1024, height = 1024;

    cv::Mat image(height, width, CV_8UC3, cv::Scalar(0, 0, 0));
    cv::Mat depthBuffer(height, width, CV_32F, cv::Scalar(std::numeric_limits<float>::max()));

    
    float focalLength = width / (2.0 * tan(90.0 * M_PI / 360.0));
    float centerX = width / 2.0;
    float centerY = height / 2.0;

    std::unordered_map<int, std::unordered_map<int, std::tuple<float, Eigen::Vector3f, cv::Vec3b>>> pixel_map;

    for (size_t i = 0; i < transformed_cloud->points.size(); ++i) {
        const auto& point = transformed_cloud->points[i];
        const auto& original_point = cloud->points[i];

        float x = width - (focalLength * point.y / point.x + centerX);
        float y = height - (focalLength * point.z / point.x + centerY);

        if (x >= 0 && x < width && y >= 0 && y < height && point.x > 0) {
            int xi = static_cast<int>(x);
            int yi = static_cast<int>(y);
            float& pixelDepth = depthBuffer.at<float>(yi, xi);
            if (point.x < pixelDepth) { 
                pixelDepth = point.x;
                cv::Vec3b color(point.b, point.g, point.r);
                Eigen::Vector3f original_coords(original_point.x, original_point.y, original_point.z);
                pixel_map[xi][yi] = std::make_tuple(pixelDepth, original_coords, color);
            }
        }
    }

    std::ofstream csv_file(file_name + ".csv");
    csv_file << "pixel_x,pixel_y,point_x,point_y,point_z" << std::endl;

    for (const auto& x_pair : pixel_map) {
        int xi = x_pair.first;
        for (const auto& y_pair : x_pair.second) {
            int yi = y_pair.first;
            auto [depth, coords, color] = y_pair.second;

            image.at<cv::Vec3b>(yi, xi) = color;
            csv_file << xi << "," << yi << "," << coords.x() << "," << coords.y() << "," << coords.z() << std::endl;
        }
    }

    csv_file.close();
    cv::imwrite(file_name, image);
}

int main(int argc, char** argv) {

    pcl::PointCloud<PointXYZRGBI>::Ptr cloud(new pcl::PointCloud<PointXYZRGBI>);
    if (pcl::io::loadPCDFile<PointXYZRGBI>(argv[1], *cloud) == -1) {
        PCL_ERROR("Couldn't read file\n");
        return -1;
    }

    bool initialize = false;

    if (argc == 10 && std::string(argv[6]) == "initial") {
        initialize = true;
    }
    else if (argc != 8) {
        std::cout << "Usage: " << argv[0] << " <PCD file> [<initial> <initial point x> <initial point y> <initial point z> <ending point x> <ending point y> <ending point z> |<direction vector x> <direction vector y>]" << std::endl;
        return -1;
    }

    float cx1 = std::stof(argv[3]);
    float cy1 = std::stof(argv[4]);
    float cz1 = std::stof(argv[5]);
    Eigen::Vector3f translation(cx1, cy1, cz1);
    Eigen::Vector3f point1(cx1, cy1, cz1);
    Eigen::Vector3f direction;

    if (initialize) {
        float cx2 = std::stof(argv[7]);
        float cy2 = std::stof(argv[8]);
        float cz2 = std::stof(argv[9]);
        Eigen::Vector3f point2(cx2, cy2, cz2);
        direction = point2 - point1;
        std::cout << "initial_direction: " << direction.x() << " " << direction.y() << " " << std::endl;
    }
    else {
        direction.x() = std::stof(argv[6]);
        direction.y() = std::stof(argv[7]);
    }

    float prior_angle = std::atan2(direction.y(), direction.x());

    pcl::PointCloud<PointXYZRGBI>::Ptr filtered_cloud(new pcl::PointCloud<PointXYZRGBI>);
    float range = 50.0f; 
    for (const auto& p : cloud->points) {
        Eigen::Vector3f point(p.x, p.y, p.z);
        if ((point - translation).norm() <= range) {
            filtered_cloud->points.push_back(p);
        }
    }

    std::string basePath = argv[2];
    size_t lastSlashPos = basePath.find_last_of('/');
    std::string directoryPath = basePath.substr(0, lastSlashPos);
    boost::filesystem::path dir(directoryPath);
    if (!boost::filesystem::exists(dir)) {
        if (!boost::filesystem::create_directories(dir)) {
            std::cerr << "Failed to create directories: " << directoryPath << std::endl;
        }
    }

    int num_rot = 4;
    auto rotations = pcTransform(num_rot, translation, prior_angle);

    for (int i = 0; i < rotations.size(); ++i) {
        // if (i == 2) continue;
        std::string file_name = basePath + "_" + std::to_string(i) + ".png";
        pc2img(filtered_cloud, rotations[i], file_name);
    }

    return 0;
}