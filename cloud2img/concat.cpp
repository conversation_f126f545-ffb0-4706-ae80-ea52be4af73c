#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>
#include <boost/filesystem.hpp>
#include <pcl/io/pcd_io.h>
#include <opencv2/opencv.hpp>

struct Vec3bHash {
    std::size_t operator()(const cv::Vec3b& color) const {
        return std::hash<int>()(color[0] * 256 * 256 + color[1] * 256 + color[2]);
    }
};

int main(int argc, char** argv) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <folder_path>" << std::endl;
        return 1;
    }

    std::string folderPath = argv[1];
    std::string outputPath = argv[2];
    std::vector<std::string> pcdFiles;

    pcl::PCDReader reader;
    pcl::PCDWriter writer;

    for (const auto& entry : boost::filesystem::directory_iterator(folderPath)) {
        if (entry.path().extension() == ".pcd" && entry.path().filename().string().find("direction") == std::string::npos) {
            std::cout << entry.path().string() << std::endl;
            pcdFiles.push_back(entry.path().string());
        }
    }

    if (pcdFiles.empty()) {
        std::cout << "No pcd files to merge." << std::endl;
        return 0;
    }

    pcl::PointCloud<pcl::PointXYZRGB>::Ptr mergedCloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    
    std::unordered_map<cv::Vec3b, cv::Vec3b, Vec3bHash> color_map = {
        {cv::Vec3b(204, 255, 4), cv::Vec3b(4, 250, 7)},  {cv::Vec3b(0, 71, 255), cv::Vec3b(51, 0, 255)},  
        {cv::Vec3b(255, 9, 224), cv::Vec3b(180, 120, 120)}, {cv::Vec3b(163, 0, 255), cv::Vec3b(0, 102, 200)}, 
        {cv::Vec3b(163, 255, 0), cv::Vec3b(0, 102, 200)}, {cv::Vec3b(255, 0, 20), cv::Vec3b(0, 102, 200)}, 
        {cv::Vec3b(255, 245, 0), cv::Vec3b(0, 102, 200)} 
    };

    for (const std::string& pcdFile : pcdFiles) {
        pcl::PointCloud<pcl::PointXYZRGB>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
        reader.read(pcdFile, *cloud);

        for (auto& p : cloud->points){
            cv::Vec3b color;
            color[0] = p.r;
            color[1] = p.g;
            color[2] = p.b;
            auto it = color_map.find(color);
            if (it != color_map.end()) {
                p.r = it->second[0];
                p.g = it->second[1];
                p.b = it->second[2];
            }
        }

        *mergedCloud += *cloud;
    }

    writer.write(outputPath, *mergedCloud);
    
    std::cout << "Merged pcd file saved as: " << outputPath << std::endl;

    return 0;
}